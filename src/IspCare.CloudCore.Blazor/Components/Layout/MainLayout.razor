@inherits LayoutComponentBase

<RadzenDialog/>
<RadzenNotification/>

<RadzenLayout>
    <RadzenHeader>
        <HeaderComponent SidebarExpanded="@_sidebarExpanded" OnSidebarToggled="@OnSidebarToggled" />
    </RadzenHeader>
    <RadzenSidebar @bind-Expanded="@_sidebarExpanded">
        <SidebarComponent />
    </RadzenSidebar>
    <RadzenContent>
        <RadzenStack Gap="1rem" Class="rz-p-4">
            @Body
        </RadzenStack>
    </RadzenContent>
</RadzenLayout>

@code {
    private bool _sidebarExpanded = true;

    private void OnSidebarToggled(bool newState)
    {
        _sidebarExpanded = newState;
        StateHasChanged();
    }
}