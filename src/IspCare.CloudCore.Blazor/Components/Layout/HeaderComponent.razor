@rendermode InteractiveServer

<div class="d-flex align-items-center justify-content-between p-4">
    <RadzenButton Icon="menu" Click="@HandleToggleSidebar" Variant="Variant.Text"/>
    <RadzenLabel Text="ISP Care - Cloud Core" Style="font-size: 1.5rem; margin-left: 12px;"/>
    <AuthorizeView>
        <Authorized>
            <RadzenButton Text="Logout" Click="@(() => Navigation.NavigateTo("/logout"))"
                          ButtonStyle="ButtonStyle.Info"/>
        </Authorized>
        <NotAuthorized>
            <RadzenButton Text="Login" Click="@(args => Console.WriteLine("hi"))"
                          ButtonStyle="ButtonStyle.Success"/>
        </NotAuthorized>
    </AuthorizeView>
</div>

@code {
    [Parameter] public EventCallback<bool> OnSidebarToggled { get; set; }
    [Parameter] public bool SidebarExpanded { get; set; } = true;

    [Inject] private NavigationManager Navigation { get; set; } = default!;

    private async Task HandleToggleSidebar()
    {
        var newState = !SidebarExpanded;
        await OnSidebarToggled.InvokeAsync(newState);
    }
}
