@rendermode InteractiveServer
@inject NavigationManager NavigationManager

<div>
    <div class="d-flex align-items-center justify-content-between p-4">
        <RadzenHeader>
            <RadzenButton Icon="menu" Click="@ToggleSidebar" Variant="Variant.Text"/>
            <RadzenLabel Text="ISP Care - Cloud Core" Style="font-size: 1.5rem; margin-left: 12px;"/>
            <AuthorizeView>
                <Authorized>
                    <RadzenButton Text="Logout" Click="@(() => NavigationManager.NavigateTo("/logout"))"
                                  ButtonStyle="ButtonStyle.Info"/>
                </Authorized>
                <NotAuthorized>
                    <RadzenButton Text="Login" Click="@(args => Console.WriteLine("hi"))"
                                  ButtonStyle="ButtonStyle.Success"/>
                </NotAuthorized>
            </AuthorizeView>
        </RadzenHeader>
    </div>
    <RadzenSidebar @bind-Expanded="@_sidebarExpanded">
        <RadzenPanelMenu>
            <RadzenPanelMenuItem Text="Home" Path="/"/>
            <AuthorizeView>
                <Authorized>
                    <RadzenPanelMenuItem Text="ISPs" Path="/isps"/>
                    <RadzenPanelMenuItem Text="POS Users" Path="/pos-users"/>
                </Authorized>
            </AuthorizeView>
        </RadzenPanelMenu>
    </RadzenSidebar>
</div>

@code {
    private bool _sidebarExpanded = true;

    private void ToggleSidebar()
    {
        _sidebarExpanded = !_sidebarExpanded;
    }

}
